import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:math' as math;
import 'habit.dart';
import 'entry.dart';
import 'database_service.dart';
// import 'enhanced_entry_dialog.dart';
// import 'habit_analytics_widgets.dart';
import 'habit_analytics_service.dart';
import 'modern_theme.dart';

/// Individual Habit Analytics Screen showing comprehensive analytics for a specific habit
class HabitAnalyticsScreen extends StatefulWidget {
  final Habit habit;

  const HabitAnalyticsScreen({super.key, required this.habit});

  @override
  State<HabitAnalyticsScreen> createState() => _HabitAnalyticsScreenState();
}

class _HabitAnalyticsScreenState extends State<HabitAnalyticsScreen>
    with TickerProviderStateMixin {
  final _databaseService = DatabaseService();

  late AnimationController _scoreAnimationController;
  late AnimationController _cardAnimationController;
  late Animation<double> _scoreAnimation;
  late Animation<double> _cardAnimation;

  // Task 1: Add scroll controllers for charts
  late ScrollController _scoreChartController;
  late ScrollController _historyChartController;

  // Add time scale support for charts
  TimeScale _scoreChartTimeScale = TimeScale.week;
  TimeScale _historyChartTimeScale = TimeScale.week;

  bool _isLoading = true;
  List<Entry> _entries = [];
  List<double> _scoreHistory = [];
  late HabitAnalyticsService _analyticsService;

  @override
  void initState() {
    super.initState();
    debugPrint(
      '[HABIT_ANALYTICS_SCREEN] === INITIALIZING ANALYTICS SCREEN ===',
    );
    debugPrint(
      '[HABIT_ANALYTICS_SCREEN] Habit: ${widget.habit.name} (ID: ${widget.habit.id})',
    );

    // Task 1: Initialize scroll controllers
    _scoreChartController = ScrollController();
    _historyChartController = ScrollController();

    _initializeAnimations();
    _loadAnalyticsData();
  }

  void _initializeAnimations() {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Initializing animations');
    _scoreAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scoreAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _scoreAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );

    _cardAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _cardAnimationController,
        curve: Curves.easeOutBack,
      ),
    );

    debugPrint('[HABIT_ANALYTICS_SCREEN] Animations initialized successfully');
  }

  Future<void> _loadAnalyticsData() async {
    try {
      debugPrint('[HABIT_ANALYTICS_SCREEN] === LOADING ANALYTICS DATA ===');
      debugPrint(
        '[HABIT_ANALYTICS_SCREEN] Loading entries for habit: ${widget.habit.id}',
      );

      setState(() {
        _isLoading = true;
      });

      // Load entries for this habit
      _entries = await _databaseService.loadEntriesForHabit(widget.habit.id);
      debugPrint('[HABIT_ANALYTICS_SCREEN] Loaded ${_entries.length} entries');

      // Initialize analytics service
      _analyticsService = HabitAnalyticsService(
        habit: widget.habit,
        entries: _entries,
      );

      // Calculate score history
      _calculateScoreHistory();

      setState(() {
        _isLoading = false;
      });

      // Start animations
      _cardAnimationController.forward();
      _scoreAnimationController.forward();

      // Task 1: Set initial scroll positions to show recent data
      _setInitialScrollPositions();

      debugPrint(
        '[HABIT_ANALYTICS_SCREEN] === ANALYTICS DATA LOADED SUCCESSFULLY ===',
      );
    } catch (e, stackTrace) {
      debugPrint(
        '[HABIT_ANALYTICS_SCREEN] ERROR: Failed to load analytics data - $e',
      );
      debugPrint('[HABIT_ANALYTICS_SCREEN] StackTrace: $stackTrace');

      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load analytics: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _calculateScoreHistory() {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Calculating score history');
    _scoreHistory.clear();

    // Calculate daily scores for the last 30 days
    final now = DateTime.now();
    for (int i = 29; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayEntries = _entries
          .where(
            (entry) =>
                entry.timestamp.year == date.year &&
                entry.timestamp.month == date.month &&
                entry.timestamp.day == date.day,
          )
          .toList();

      double dayScore = 0.0;
      if (dayEntries.isNotEmpty) {
        if (widget.habit.type == HabitType.boolean) {
          // COMPREHENSIVE DEBUGGING: Calculate boolean day score
          debugPrint(
            '[HABIT_ANALYTICS_SCREEN] Calculating boolean day score for ${dayEntries.length} entries',
          );
          dayScore =
              dayEntries.any((e) {
                debugPrint(
                  '[HABIT_ANALYTICS_SCREEN] Checking entry ${e.id}: booleanValue = ${e.booleanValue}',
                );
                return e.booleanValue == true;
              })
              ? 1.0
              : 0.0;
          debugPrint('[HABIT_ANALYTICS_SCREEN] Boolean day score: $dayScore');
        } else {
          // COMPREHENSIVE DEBUGGING: Calculate numerical day score
          debugPrint(
            '[HABIT_ANALYTICS_SCREEN] Calculating numerical day score for ${dayEntries.length} entries',
          );
          final totalValue = dayEntries.fold<double>(0.0, (sum, entry) {
            final entryValue = entry.numericalValue ?? 0.0;
            debugPrint(
              '[HABIT_ANALYTICS_SCREEN] Entry ${entry.id} value: $entryValue',
            );
            return sum + entryValue;
          });
          debugPrint('[HABIT_ANALYTICS_SCREEN] Total value: $totalValue');
          final target = widget.habit.targetValue ?? 1.0;
          dayScore = math.min(totalValue / target, 1.0);
          debugPrint(
            '[HABIT_ANALYTICS_SCREEN] Target: $target, Day score: $dayScore',
          );
        }
      }
      _scoreHistory.add(dayScore);
    }

    debugPrint(
      '[HABIT_ANALYTICS_SCREEN] Score history calculated: ${_scoreHistory.length} days',
    );
  }

  @override
  void dispose() {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Disposing analytics screen');
    _scoreAnimationController.dispose();
    _cardAnimationController.dispose();

    // Task 1: Dispose scroll controllers
    _scoreChartController.dispose();
    _historyChartController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('[HABIT_ANALYTICS_SCREEN] Building analytics screen UI');

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: AppBar(
        title: Text(
          '${widget.habit.name} Analytics',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w600,
            color: ModernTheme.textColor,
          ),
        ),
        backgroundColor: ModernTheme.cardColor,
        elevation: 0,
        iconTheme: IconThemeData(color: ModernTheme.textColor),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildAnalyticsContent(),
    );
  }

  Widget _buildAnalyticsContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverviewCard(),
          const SizedBox(height: 16),
          _buildScoreChart(),
          const SizedBox(height: 16),
          _buildHistoryChart(),
          const SizedBox(height: 16),
          _buildRecentEntries(),
        ],
      ),
    );
  }

  Widget _buildOverviewCard() {
    final totalEntries = _entries.length;
    final currentStreak = _calculateCurrentStreak();
    final averageScore = _calculateAverageScore();

    return AnimatedBuilder(
      animation: _cardAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _cardAnimation.value,
          child: Card(
            color: ModernTheme.cardColor,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Overview',
                    style: GoogleFonts.inter(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: ModernTheme.textColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem('Total Entries', totalEntries.toString()),
                      _buildStatItem('Current Streak', '$currentStreak days'),
                      _buildStatItem(
                        'Average Score',
                        '${(averageScore * 100).toInt()}%',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: ModernTheme.primaryColor,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: 12,
            color: ModernTheme.textColor.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildScoreChart() {
    final theme = Theme.of(context);
    return Card(
      color: ModernTheme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Score Chart',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: ModernTheme.textColor,
                  ),
                ),
                _buildTimeScaleButton(theme, _scoreChartTimeScale, (scale) {
                  debugPrint(
                    '[HABIT_ANALYTICS_SCREEN] Score chart time scale changed to: $scale',
                  );
                  setState(() => _scoreChartTimeScale = scale);
                  // Task 1: Re-trigger scroll to recent data when switching to Day view
                  if (scale == TimeScale.day) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _scrollToRecentData(_scoreChartController, scale);
                    });
                  }
                }),
              ],
            ),
            const SizedBox(height: 16),
            // Task 2: Implement sticky Y-axis for Score Chart
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  // Y-axis container with sufficient width to prevent overflow
                  SizedBox(
                    width:
                        65.0, // Increased width to prevent "right overflowed by 6.0 pixels" error
                    child: _buildStickyYAxisForScore(theme),
                  ),
                  // Task 2: Scrollable chart area
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      controller: _scoreChartController,
                      child: SizedBox(
                        width: _calculateChartWidth(_scoreChartTimeScale),
                        child: _buildScoreLineChart(theme, hideYAxis: true),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentEntries() {
    final recentEntries = _entries.take(10).toList();

    return Card(
      color: ModernTheme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Entries',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: ModernTheme.textColor,
              ),
            ),
            const SizedBox(height: 16),
            if (recentEntries.isEmpty)
              Text(
                'No entries yet',
                style: GoogleFonts.inter(
                  color: ModernTheme.textColor.withOpacity(0.7),
                ),
              )
            else
              ...recentEntries.map((entry) => _buildEntryItem(entry)),
          ],
        ),
      ),
    );
  }

  Widget _buildEntryItem(Entry entry) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _formatDate(entry.timestamp),
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w500,
                  color: ModernTheme.textColor,
                ),
              ),
              if (entry.note?.isNotEmpty == true)
                Text(
                  entry.note!,
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    color: ModernTheme.textColor.withOpacity(0.7),
                  ),
                ),
            ],
          ),
          Text(
            entry.type == EntryType.boolean
                ? (entry.booleanValue == true ? '✓' : '✗')
                : '${entry.numericalValue?.toStringAsFixed(1) ?? '0'} ${widget.habit.unit ?? ''}',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w600,
              color: entry.type == EntryType.boolean
                  ? (entry.booleanValue == true ? Colors.green : Colors.red)
                  : ModernTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  int _calculateCurrentStreak() {
    if (_entries.isEmpty) return 0;

    int streak = 0;
    final now = DateTime.now();

    for (int i = 0; i < 365; i++) {
      final date = now.subtract(Duration(days: i));
      final hasEntry = _entries.any(
        (entry) =>
            entry.timestamp.year == date.year &&
            entry.timestamp.month == date.month &&
            entry.timestamp.day == date.day &&
            (entry.type == EntryType.boolean
                ? entry.booleanValue == true
                : (entry.numericalValue ?? 0) > 0),
      );

      if (hasEntry) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  double _calculateAverageScore() {
    if (_scoreHistory.isEmpty) return 0.0;
    return _scoreHistory.reduce((a, b) => a + b) / _scoreHistory.length;
  }

  // Task 1: Set initial scroll positions to show recent data
  void _setInitialScrollPositions() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToRecentData(_scoreChartController, _scoreChartTimeScale);
      _scrollToRecentData(_historyChartController, _historyChartTimeScale);
    });
  }

  // Task 1: Scroll charts to show recent data (most recent data on the right)
  void _scrollToRecentData(ScrollController controller, TimeScale timeScale) {
    if (!controller.hasClients) return;

    try {
      final maxScrollExtent = controller.position.maxScrollExtent;
      if (maxScrollExtent <= 0) return;

      // Task 1: Jump to maximum scroll extent to show most recent data
      controller.jumpTo(maxScrollExtent);
      debugPrint(
        '[HABIT_ANALYTICS_SCREEN] Scrolled ${controller.runtimeType} to show recent data at offset: $maxScrollExtent',
      );
    } catch (e) {
      debugPrint('[HABIT_ANALYTICS_SCREEN] Error setting scroll position: $e');
    }
  }

  // Build time scale selection button
  Widget _buildTimeScaleButton(
    ThemeData theme,
    TimeScale currentScale,
    Function(TimeScale) onChanged,
  ) {
    return OutlinedButton(
      onPressed: () => _showTimeScaleMenu(theme, currentScale, onChanged),
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        minimumSize: Size.zero,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _getTimeScaleDisplayName(currentScale),
            style: GoogleFonts.inter(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 4),
          const Icon(Icons.keyboard_arrow_down, size: 16),
        ],
      ),
    );
  }

  void _showTimeScaleMenu(
    ThemeData theme,
    TimeScale currentScale,
    Function(TimeScale) onChanged,
  ) {
    showMenu(
      context: context,
      position: const RelativeRect.fromLTRB(100, 100, 0, 0),
      items: TimeScale.values
          .map(
            (scale) => PopupMenuItem(
              value: scale,
              child: Text(_getTimeScaleDisplayName(scale)),
            ),
          )
          .toList(),
    ).then((value) {
      if (value != null) {
        onChanged(value);
      }
    });
  }

  String _getTimeScaleDisplayName(TimeScale scale) {
    switch (scale) {
      case TimeScale.day:
        return 'Day';
      case TimeScale.week:
        return 'Week';
      case TimeScale.month:
        return 'Month';
      case TimeScale.quarter:
        return 'Quarter';
      case TimeScale.year:
        return 'Year';
    }
  }

  // Calculate chart width based on time scale
  double _calculateChartWidth(TimeScale timeScale) {
    switch (timeScale) {
      case TimeScale.day:
        return 35.0 * 30; // 30 days
      case TimeScale.week:
        return 50.0 * 12; // 12 weeks
      case TimeScale.month:
        return 60.0 * 12; // 12 months
      default:
        return 600.0;
    }
  }

  // COMPREHENSIVE DEBUGGING: Delete entry and save habit
  Future<void> _deleteEntry(Entry entry) async {
    try {
      debugPrint('[HABIT_ANALYTICS_SCREEN] === DELETING ENTRY ===');
      debugPrint('[HABIT_ANALYTICS_SCREEN] Deleting entry and updating habit');
      debugPrint(
        '[HABIT_ANALYTICS_SCREEN] Habit: ${widget.habit.name} (ID: ${widget.habit.id})',
      );
      debugPrint('[HABIT_ANALYTICS_SCREEN] Entry to delete: ${entry.id}');

      await _databaseService.deleteEntry(entry.id);
      widget.habit.entries.removeWhere((e) => e.id == entry.id);
      await _databaseService.saveHabit(widget.habit);

      debugPrint(
        '[HABIT_ANALYTICS_SCREEN] Entry deleted and habit saved successfully',
      );

      await _loadAnalyticsData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Entry deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }

      debugPrint('[HABIT_ANALYTICS_SCREEN] === ENTRY DELETION COMPLETE ===');
    } catch (e, stackTrace) {
      debugPrint('[HABIT_ANALYTICS_SCREEN] ERROR: Failed to delete entry - $e');
      debugPrint('[HABIT_ANALYTICS_SCREEN] StackTrace: $stackTrace');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete entry: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Task 2 & 3: Build Score Line Chart with dynamic scaling and Y-axis hiding
  Widget _buildScoreLineChart(ThemeData theme, {bool hideYAxis = false}) {
    final dataPoints = _analyticsService.getScoreDataForChart(
      _scoreChartTimeScale,
    );

    if (dataPoints.isEmpty) {
      return Center(
        child: Text(
          'No data available',
          style: GoogleFonts.inter(color: theme.colorScheme.onSurfaceVariant),
        ),
      );
    }

    // Task 3: Calculate maximum Y-value for automatic zooming
    final maxDataValue = dataPoints
        .map((point) => point.value)
        .reduce((a, b) => a > b ? a : b);
    final dynamicMaxY = (maxDataValue * 1.2).clamp(0.1, 1.0);

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        maxY: dynamicMaxY,
        minY: 0,
        titlesData: FlTitlesData(
          show: true,
          // Task 2: Add right padding to prevent chart cutoff
          rightTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 20,
              getTitlesWidget: (value, meta) =>
                  const SizedBox.shrink(), // Invisible but reserves space
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < dataPoints.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Text(
                      dataPoints[index].label,
                      style: GoogleFonts.inter(
                        fontSize: 9,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: !hideYAxis),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [
          LineChartBarData(
            spots: dataPoints.asMap().entries.map((entry) {
              return FlSpot(
                entry.key.toDouble(),
                entry.value.value.clamp(0.0, 1.0),
              );
            }).toList(),
            isCurved: false,
            color: theme.colorScheme.primary,
            barWidth: 3,
            dotData: FlDotData(show: true),
          ),
        ],
      ),
    );
  }

  // Task 2: Build sticky Y-axis for Score Chart
  Widget _buildStickyYAxisForScore(ThemeData theme) {
    final dataPoints = _analyticsService.getScoreDataForChart(
      _scoreChartTimeScale,
    );
    if (dataPoints.isEmpty) return Container();

    final maxDataValue = dataPoints
        .map((point) => point.value)
        .reduce((a, b) => a > b ? a : b);
    final dynamicMaxY = (maxDataValue * 1.2).clamp(0.1, 1.0);

    return LineChart(
      LineChartData(
        gridData: FlGridData(show: false),
        maxY: dynamicMaxY,
        minY: 0,
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          leftTitles: AxisTitles(
            axisNameWidget: Text(
              'Score',
              style: GoogleFonts.inter(
                fontSize: 10,
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize:
                  45, // Task 1: Increased reserved size to prevent overflow
              getTitlesWidget: (value, meta) {
                return Text(
                  '${(value * 100).toInt()}%',
                  style: GoogleFonts.inter(
                    fontSize: 9,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                );
              },
            ),
          ),
        ),
        borderData: FlBorderData(show: false),
        lineBarsData: [],
      ),
    );
  }

  // Build History Chart with similar enhancements
  Widget _buildHistoryChart() {
    final theme = Theme.of(context);
    return Card(
      color: ModernTheme.cardColor,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'History Chart',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: ModernTheme.textColor,
                  ),
                ),
                _buildTimeScaleButton(theme, _historyChartTimeScale, (scale) {
                  debugPrint(
                    '[HABIT_ANALYTICS_SCREEN] History chart time scale changed to: $scale',
                  );
                  setState(() => _historyChartTimeScale = scale);
                  // Task 1: Re-trigger scroll to recent data when switching to Day view
                  if (scale == TimeScale.day) {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _scrollToRecentData(_historyChartController, scale);
                    });
                  }
                }),
              ],
            ),
            const SizedBox(height: 16),
            // Task 2: Implement sticky Y-axis for History Chart
            SizedBox(
              height: 200,
              child: Row(
                children: [
                  // Y-axis container with sufficient width to prevent overflow
                  SizedBox(
                    width:
                        65.0, // Increased width to prevent "right overflowed by 6.0 pixels" error
                    child: _buildStickyYAxisForHistory(theme),
                  ),
                  // Task 2: Scrollable chart area
                  Expanded(
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      controller: _historyChartController,
                      child: SizedBox(
                        width: _calculateChartWidth(_historyChartTimeScale),
                        child: _buildHistoryBarChart(theme, hideYAxis: true),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Task 2 & 3: Build History Bar Chart with dynamic scaling and Y-axis hiding
  Widget _buildHistoryBarChart(ThemeData theme, {bool hideYAxis = false}) {
    return FutureBuilder<List<ChartDataPoint>>(
      future: _analyticsService.getHistoryDataForChart(_historyChartTimeScale),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading data',
              style: GoogleFonts.inter(color: theme.colorScheme.onSurfaceVariant),
            ),
          );
        }
        
        final dataPoints = snapshot.data ?? [];
        
        if (dataPoints.isEmpty) {
          return Center(
            child: Text(
              'No data available',
              style: GoogleFonts.inter(color: theme.colorScheme.onSurfaceVariant),
            ),
          );
        }

        // Task 3: Calculate maximum Y-value for automatic zooming
        final maxDataValue = dataPoints
            .map((point) => point.value)
            .reduce((a, b) => a > b ? a : b);
        final dynamicMaxY = (maxDataValue * 1.2).clamp(1.0, double.infinity);

        return BarChart(
          BarChartData(
            gridData: FlGridData(show: false),
            maxY: dynamicMaxY,
            minY: 0,
            titlesData: FlTitlesData(
              show: true,
              rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              bottomTitles: AxisTitles(
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize: 35,
                  interval: 1,
                  getTitlesWidget: (value, meta) {
                    final index = value.toInt();
                    if (index >= 0 && index < dataPoints.length) {
                      return Padding(
                        padding: const EdgeInsets.only(top: 6),
                        child: Text(
                          dataPoints[index].label,
                          style: GoogleFonts.inter(
                            fontSize: 9,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      );
                    }
                    return const Text('');
                  },
                ),
              ),
              leftTitles: AxisTitles(
                sideTitles: SideTitles(showTitles: !hideYAxis),
              ),
            ),
            borderData: FlBorderData(show: false),
            barGroups: dataPoints.asMap().entries.map((entry) {
              return BarChartGroupData(
                x: entry.key,
                barRods: [
                  BarChartRodData(
                    toY: entry.value.value,
                    color: theme.colorScheme.primary,
                    width: 20,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ],
              );
            }).toList(),
          ),
        );
  }

  // Task 2: Build sticky Y-axis for History Chart
  Widget _buildStickyYAxisForHistory(ThemeData theme) {
    return FutureBuilder<List<ChartDataPoint>>(
      future: _analyticsService.getHistoryDataForChart(_historyChartTimeScale),
      builder: (context, snapshot) {
        final dataPoints = snapshot.data ?? [];
        if (dataPoints.isEmpty) return Container();

        final maxDataValue = dataPoints
            .map((point) => point.value)
            .reduce((a, b) => a > b ? a : b);
        final dynamicMaxY = (maxDataValue * 1.2).clamp(1.0, double.infinity);

        return BarChart(
          BarChartData(
            gridData: FlGridData(show: false),
            maxY: dynamicMaxY,
            minY: 0,
            titlesData: FlTitlesData(
              show: true,
              rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              leftTitles: AxisTitles(
                axisNameWidget: Text(
                  'Count',
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    color: theme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                sideTitles: SideTitles(
                  showTitles: true,
                  reservedSize:
                      45, // Task 1: Increased reserved size to prevent overflow
                  getTitlesWidget: (value, meta) {
                    return Text(
                      value.toInt().toString(),
                      style: GoogleFonts.inter(
                        fontSize: 9,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    );
                  },
                ),
              ),
            ),
            borderData: FlBorderData(show: false),
            barGroups: [],
          ),
        );
      },
    );
  }
}

// Custom painter for the score chart
class ScoreChartPainter extends CustomPainter {
  final List<double> scores;
  final double animationValue;

  ScoreChartPainter({required this.scores, required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    if (scores.isEmpty) return;

    final paint = Paint()
      ..color = ModernTheme.primaryColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final stepX = size.width / (scores.length - 1);

    for (int i = 0; i < scores.length; i++) {
      final x = i * stepX;
      final y = size.height - (scores[i] * size.height * animationValue);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);

    // Draw points
    final pointPaint = Paint()
      ..color = ModernTheme.primaryColor
      ..style = PaintingStyle.fill;

    for (int i = 0; i < scores.length; i++) {
      final x = i * stepX;
      final y = size.height - (scores[i] * size.height * animationValue);
      canvas.drawCircle(Offset(x, y), 3, pointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
